import React, { useState, useEffect } from "react";

interface SmallWebsiteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLearnMore: () => void;
}

const SmallWebsiteModal: React.FC<SmallWebsiteModalProps> = ({
  isOpen,
  onClose,
  onLearnMore,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  // Handle animation states
  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // Small delay to ensure DOM is ready, then start animation
      setTimeout(() => setIsAnimating(true), 10);
    } else {
      setIsAnimating(false);
      // Wait for animation to complete before removing from DOM
      setTimeout(() => setShouldRender(false), 300);
    }
  }, [isOpen]);

  if (!shouldRender) return null;

  return (
    <div
      className={`fixed bottom-6 right-6 z-50 transition-all duration-300 ease-out transform ${
        isAnimating
          ? "translate-y-0 opacity-100 scale-100"
          : "translate-y-4 opacity-0 scale-95"
      }`}
    >
      <div className="bg-white rounded-2xl shadow-2xl border border-gray-100 p-6 max-w-sm relative overflow-hidden">
        {/* Decorative Background Elements */}
        <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-purple-500/10 to-blue-500/10 rounded-full blur-lg"></div>

        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 text-lg transition-all duration-200 transform hover:scale-110 hover:rotate-90"
          aria-label="Close modal"
        >
          ×
        </button>

        {/* Content */}
        <div className="relative z-10">
          {/* Icon and Title */}
          <div className="flex items-center mb-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
              <span className="text-white text-sm">🌐</span>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-bold text-gray-900">Welcome to GTI</h3>
              <p className="text-xs text-gray-500">Global Technology Interface</p>
            </div>
          </div>

          {/* Brief Description */}
          <p className="text-sm text-gray-600 mb-4 leading-relaxed">
            Discover cutting-edge technologies and connect with innovators worldwide.
          </p>

          {/* Quick Stats */}
          <div className="flex justify-between mb-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">10K+</div>
              <div className="text-xs text-gray-500">Technologies</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">5K+</div>
              <div className="text-xs text-gray-500">Opportunities</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">50+</div>
              <div className="text-xs text-gray-500">Countries</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={onLearnMore}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Learn More
            </button>
            <button
              onClick={onClose}
              className="px-3 py-2 text-gray-500 hover:text-gray-700 text-sm transition-all duration-200 transform hover:scale-105"
            >
              Later
            </button>
          </div>
        </div>

        {/* Subtle Animation Elements */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-blue-400 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-purple-400 rounded-full opacity-20 animate-pulse delay-1000"></div>
      </div>
    </div>
  );
};

export default SmallWebsiteModal;
