import React, { useState } from "react";
import { useFormik } from "formik";
import { Helmet } from "react-helmet";

import axios from "axios";

import "./style.css";

import {
  companySignupSchema,
  personalSignupSchema,
} from "../validations/signupValidations";
import { Document, FormValues } from "../../shared/interface";
import {
  DOCUMENT_TYPE,
  metaData,
  presignedData,
  title,
  CONTENT_TYPE,
  CONTENT_TYPE_DOC,
  FILE_PATH,
  FILE_TYPE,
} from "../constants";
import { PROFILE_TYPES } from "../../shared/enum";
import { PersonalFormValues } from "../../shared/interface";
import globe from "../../assests/home/<USER>";
import CompanyDetails from "./CompanyDetails";
import { createUser } from "../../api/user";
import { isSuccess, notify } from "../../utils";
import TechnologyDetails from "./TechnologyDetails";
import OpportunityDetails from "./OpportunityDetails";
import SuccessModal from "./SuccessModal";
import { RequestMethods } from "../../shared/RequestMethods";
import PersonalInformation from "./PersonalInformation";
import UserTypeSelection from "./UserTypeSelection";
import ReviewSubmit from "./ReviewSubmit";

const Signup = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const [profileType, setProfileType] = useState<PROFILE_TYPES | null>(null);
  const [country, setCountry] = useState("IN");
  const [step, setStep] = useState(0);
  const [successModal, setSuccessModal] = useState<boolean>(false);
  const [state, setState] = useState("LOADING");
  const [message, setMessage] = useState("");
  const [isSubmitting] = useState(false);

  const Error_modal = document.getElementById("error-modal");

  const [comp_logo, setCompLogo] = useState<FormValues["logo"]>(null);
  const [comp_documents] = useState<FormValues["documents"]>([]);
  const [comp_documents_start] = useState<FormValues["s_documents"]>({
    document: null,
    documentType: DOCUMENT_TYPE.OPEN,
  });
  const [uploadLogo, setUploadLogoM] = useState(false);

  const handleProfileTypes = (currentProfileType: PROFILE_TYPES) => {
    setProfileType(currentProfileType);
  };

  const handleCountry = (currentCountry: string) => {
    setCountry(currentCountry);
  };

  const handleSuccessModal = (
    isOpen: boolean,
    state: string,
    message: string
  ) => {
    setSuccessModal(isOpen);
    setState(state);
    setMessage(message);
  };

  const handleGeneralProfile = async (values: PersonalFormValues) => {
    if (!profileType) {
      console.error("Profile type is required");
      return;
    }

    handleSuccessModal(true, "Please Wait", "LOADING");

    const data = {
      userType: profileType,
      fullName: values.name,
      email: values.email,
      countryCode: country,
      phoneNumber: values.phone.toString(),
      referenceCode: values.ref,
      password: values.password,
    };
    const res = await createUser(data);
    console.log({ success: isSuccess(res) });
    if (isSuccess(res)) {
      console.log({ success: isSuccess(res) });
      handleSuccessModal(true, "Registered successfully!", "SUCCESS");
      notify("Registered successfully!", "success");
    } else {
      handleSuccessModal(true, "Registered failed!", "ERROR");
      notify("Registration failed!", "error");
    }
  };

  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: RequestMethods.POST,
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data: data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
      })
      .catch(function (error) {
        result = "error";
      });

    return result;
  };

  const toggle_error = () => {
    if (Error_modal?.hasAttribute("hidden")) {
      Error_modal?.classList.remove("hidden");
      Error_modal?.classList.add("container");
    } else {
      Error_modal?.classList.remove("container");
      Error_modal?.classList.add("hidden");
    }
  };

  const postDocument = async (signed: string, file: File | null) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE_DOC,
        "Access-Control-Allow-Origin": true,
      },
      data: file,
    };
    if (file) {
      await axios(config)
        .then(function (response) {})
        .catch(function (error) {
          toggle_error();
        });
    }
  };

  const postFile = async (signed: string) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: comp_logo,
    };

    await axios(config)
      .then(function (response) {})
      .catch(function (error) {
        toggle_error();
      });
  };

  const handleCompanySubmit = async (values: FormValues) => {
    let signedLogoURL: string = "";

    const signedData: presignedData = {
      fileName: values?.logo?.name || values.name,
      filePath: FILE_PATH.COMPANY_LOGO,
      fileType: FILE_TYPE.PNG,
    };
    signedLogoURL = await getPresigned(signedData);
    postFile(signedLogoURL);
    comp_documents.push(comp_documents_start);

    const temp_docs: any[] = [];
    if (comp_documents) {
      Array.from(comp_documents).forEach(async (document: Document, i) => {
        let signedDocumentData: presignedData = {
          fileName: document.document?.name || values.name,
          filePath: FILE_PATH.COMPANY_DOCS,
          fileType: FILE_TYPE.PDF,
        };
        let tempurl = (await getPresigned(signedDocumentData)) + " ";
        temp_docs.push({
          document: tempurl,
          documentType: document.documentType,
        });

        postDocument(tempurl, document.document);
      });
    }

    const data = {
      userType: profileType,
      fullName: personalFormik.values.name,
      email: personalFormik.values.email,
      countryCode: country,
      phoneNumber: personalFormik.values.phone.toString(),
      referenceCode: personalFormik.values.ref,
      password: personalFormik.values.password,
      companyId: null,
      companyDetails: {
        name: values.name,
        logo: signedLogoURL.split("?")[0],
        description: values.description,
        address: values.address,
        website: values.website,
        country: values.country,
        companyTurnover: values.turnover,
      },
      companyDocuments: temp_docs,
    };

    let userId = null;

    const response = await axios.post(
      `${process.env.REACT_APP_BASE_API}/users`,
      data
    );
    const responseData = response?.data?.message;
    userId = responseData?._id;

    return userId;
  };

  const handleUserRegistration = async () => {
    const userId = await handleCompanySubmit({
      name: companyFormik?.values?.name,
      description: companyFormik?.values?.description,
      address: companyFormik?.values?.address,
      website: companyFormik?.values?.website,
      country: companyFormik?.values?.country,
      turnover: companyFormik?.values?.turnover,
      logo: companyFormik?.values?.logo,
      documents: companyFormik?.values?.documents,
      s_documents: companyFormik?.values?.s_documents,
    });

    return userId || "";
  };

  const handleNext = () => {
    const filteredSteps = getSteps();
    const nextStep = step + 1;
    if (nextStep < filteredSteps.length) {
      setStep(nextStep);
    }
  };

  const handlePrev = () => {
    if (step > 0) {
      setStep(step - 1);
    }
  };

  const personalFormik = useFormik<PersonalFormValues>({
    initialValues: {
      name: "",
      email: "",
      password: "",
      country: "",
      phone: "",
      ref: "",
    },
    validationSchema: personalSignupSchema,
    onSubmit: (values, formikHelpers) => {
      // Always go to next step, don't submit immediately
      handleNext();
    },
  });

  const companyFormik = useFormik<FormValues>({
    initialValues: {
      name: "",
      description: "",
      address: "",
      website: "",
      country: "",
      turnover: "",
      logo: null,
      documents: [],
      s_documents: { document: null, documentType: DOCUMENT_TYPE.OPEN },
    },
    validationSchema: companySignupSchema,
    onSubmit: (values, formikHelpers) => {
      handleNext();
    },
  });

  const getSteps = () => {
    const allSteps = [
      {
        id: 0,
        title: "Choose Your Path",
        subtitle: "Select your user type",
        component: "UserTypeSelection",
        required: true,
      },
      {
        id: 1,
        title: "Personal Details",
        subtitle: "Your basic information",
        component: "PersonalInformation",
        required: true,
      },
      {
        id: 2,
        title: "Company Information",
        subtitle: "About your organization",
        component: "CompanyDetails",
        required:
          profileType === PROFILE_TYPES.DISPLAYER ||
          profileType === PROFILE_TYPES.SCOUTER,
      },
      {
        id: 3,
        title:
          profileType === PROFILE_TYPES.DISPLAYER
            ? "Technology Details"
            : "Opportunity Details",
        subtitle:
          profileType === PROFILE_TYPES.DISPLAYER
            ? "Your products & services"
            : "Your requirements",
        component:
          profileType === PROFILE_TYPES.DISPLAYER
            ? "TechnologyDetails"
            : "OpportunityDetails",
        required:
          profileType === PROFILE_TYPES.DISPLAYER ||
          profileType === PROFILE_TYPES.SCOUTER,
      },
      {
        id: 4,
        title: "Review & Submit",
        subtitle: "Confirm your information",
        component: "ReviewSubmit",
        required: true,
      },
    ];

    return allSteps.filter((step) => step.required);
  };

  const steps = getSteps();

  return (
    <div className="modern-signup-overlay">
      <Helmet>
        <title>Join GTI | Create Your Account</title>
        <meta
          name="description"
          key="description"
          content="Join the Global Technology Innovation platform and connect with innovators worldwide"
        />
        <meta
          name="title"
          key="title"
          content="Join GTI | Create Your Account"
        />
        <meta property="og:title" content="Join GTI | Create Your Account" />
        <meta
          property="og:description"
          content="Join the Global Technology Innovation platform and connect with innovators worldwide"
        />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/signup`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Join GTI | Create Your Account" />
        <meta
          name="twitter:description"
          content="Join the Global Technology Innovation platform and connect with innovators worldwide"
        />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>

      <div className="modern-signup-container">
        {/* Progress Header */}
        <div className="signup-progress-header">
          <div className="progress-steps">
            {steps.map((stepItem, index) => (
              <div
                key={stepItem.id}
                className={`progress-step ${
                  index < step
                    ? "completed"
                    : index === step
                    ? "actives"
                    : "upcoming"
                }`}
              >
                <div className="step-circle">
                  {index < step ? (
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                    </svg>
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <div className="step-info">
                  <div className="step-title">{stepItem.title}</div>
                  <div className="step-subtitle">{stepItem.subtitle}</div>
                </div>
              </div>
            ))}
          </div>
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{
                width: `${((step + 1) / steps.length) * 100}%`,
                transition: "width 0.7s cubic-bezier(0.4, 0, 0.2, 1)",
              }}
            />
          </div>
        </div>

        {/* Step Content */}
        <div className="signup-step-content">
          <div className="step-wrapper">
            {step === 0 && (
              <UserTypeSelection
                profileType={profileType}
                handleProfileTypes={handleProfileTypes}
                onNext={handleNext}
              />
            )}
            {step === 1 && profileType && (
              <PersonalInformation
                profileType={profileType}
                country={country}
                personalFormik={personalFormik}
                handleCountry={handleCountry}
                handleProfileTypes={handleProfileTypes}
              />
            )}
            {step === 2 &&
              (profileType === PROFILE_TYPES.DISPLAYER ||
                profileType === PROFILE_TYPES.SCOUTER) && (
                <CompanyDetails
                  companyFormik={companyFormik}
                  uploadLogo={uploadLogo}
                  toggle_error={toggle_error}
                  comp_logo={comp_logo}
                  setUploadLogoM={setUploadLogoM}
                  setCompLogo={setCompLogo}
                />
              )}
            {step === 3 && profileType === PROFILE_TYPES.DISPLAYER && (
              <TechnologyDetails
                handleTechnologySubmit={handleUserRegistration}
                handleSuccessModal={handleSuccessModal}
              />
            )}
            {step === 3 && profileType === PROFILE_TYPES.SCOUTER && (
              <OpportunityDetails
                handleTechnologySubmit={handleUserRegistration}
                handleSuccessModal={handleSuccessModal}
              />
            )}
            {step === getSteps().length - 1 && (
              <ReviewSubmit
                personalData={personalFormik.values}
                companyData={companyFormik.values}
                profileType={profileType}
                onSubmit={() => {
                  if (profileType === PROFILE_TYPES.GENERAL_SUBSCRIBER) {
                    handleGeneralProfile(personalFormik.values);
                  } else {
                    // Handle business user registration
                    handleUserRegistration();
                  }
                }}
                onPrev={handlePrev}
                isSubmitting={isSubmitting}
              />
            )}
          </div>
        </div>
      </div>

      {successModal && (
        <SuccessModal
          show={successModal}
          state={state}
          message={message}
          toggle={handleSuccessModal}
        />
      )}
    </div>
  );
};

export default Signup;
